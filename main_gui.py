#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام إدارة أعمال الإدارة الهندسية
Main GUI for Engineering Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager
import logging

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")

    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window = ttk_bs.Window(
            title="تسجيل الدخول - نظام إدارة أعمال الإدارة الهندسية",
            themename="cosmo",
            size=(400, 300),
            position=(100, 100)
        )
        
        # إعداد النافذة
        self.window.resizable(False, False)
        
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان النظام مع تصميم محسن
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = ttk_bs.Label(
            title_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية",
            font=self.title_font,
            bootstyle="primary"
        )
        title_label.pack()

        subtitle_label = ttk_bs.Label(
            title_frame,
            text="Engineering Management System",
            font=("Segoe UI", 10, "italic"),
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(5, 0))

        # إطار تسجيل الدخول مع تصميم محسن
        login_frame = ttk_bs.LabelFrame(
            main_frame,
            text="تسجيل الدخول",
            padding=20,
            bootstyle="info"
        )
        login_frame.pack(fill=tk.X, pady=10)

        # حقل اسم المستخدم
        ttk_bs.Label(
            login_frame,
            text="👤 اسم المستخدم:",
            font=self.normal_font
        ).pack(anchor=tk.W, pady=(0, 5))

        self.username_entry = ttk_bs.Entry(
            login_frame,
            font=self.normal_font,
            width=35,
            bootstyle="info"
        )
        self.username_entry.pack(pady=(0, 15), ipady=5)
        self.username_entry.insert(0, "admin")  # قيمة افتراضية للاختبار

        # حقل كلمة المرور
        ttk_bs.Label(
            login_frame,
            text="🔒 كلمة المرور:",
            font=self.normal_font
        ).pack(anchor=tk.W, pady=(0, 5))

        self.password_entry = ttk_bs.Entry(
            login_frame,
            font=self.normal_font,
            width=35,
            show="*",
            bootstyle="info"
        )
        self.password_entry.pack(pady=(0, 20), ipady=5)
        self.password_entry.insert(0, "admin123")  # قيمة افتراضية للاختبار

        # أزرار مع تصميم محسن
        button_frame = ttk_bs.Frame(login_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        login_btn = ttk_bs.Button(
            button_frame,
            text="🚀 تسجيل الدخول",
            bootstyle="success",
            command=self.login,
            width=20
        )
        login_btn.pack(side=tk.RIGHT, padx=(5, 0), ipady=8)

        cancel_btn = ttk_bs.Button(
            button_frame,
            text="❌ إلغاء",
            bootstyle="secondary",
            command=self.window.quit,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, ipady=8)
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.window.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
        
        self.window.mainloop()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        if self.auth_manager.authenticate(username, password):
            messagebox.showinfo("نجح", f"مرحباً {self.auth_manager.current_user['full_name']}")
            self.window.destroy()
            self.on_success_callback()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

class MainApplication:
    """التطبيق الرئيسي"""
    
    def __init__(self):
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.menu_font = ("Segoe UI", 10)

    def start(self):
        """بدء التطبيق"""
        # عرض نافذة تسجيل الدخول
        login_window = LoginWindow(self.auth_manager, self.show_main_window)
        login_window.show()
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        self.window = ttk_bs.Window(
            title="نظام إدارة أعمال الإدارة الهندسية",
            themename="cosmo",
            size=(1200, 800),
            position=(50, 50)
        )
        
        self.create_menu()
        self.create_main_interface()
        
        self.window.mainloop()
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إعدادات النظام", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.window.quit)
        
        # قائمة المشاريع
        projects_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المشاريع", menu=projects_menu)
        projects_menu.add_command(label="إدارة المشاريع", command=self.show_projects)
        projects_menu.add_command(label="مشروع جديد", command=self.new_project)
        
        # قائمة المباني
        buildings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المباني والمرافق", menu=buildings_menu)
        buildings_menu.add_command(label="إدارة المباني", command=self.show_buildings)
        buildings_menu.add_command(label="مبنى جديد", command=self.new_building)
        
        # قائمة الصيانة
        maintenance_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الصيانة", menu=maintenance_menu)
        maintenance_menu.add_command(label="بلاغات الأعطال", command=self.show_maintenance_requests)
        maintenance_menu.add_command(label="الصيانة الوقائية", command=self.show_preventive_maintenance)
        maintenance_menu.add_command(label="سجل الصيانة", command=self.show_maintenance_history)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        reports_menu.add_command(label="تقارير المشاريع", command=self.show_project_reports)
        reports_menu.add_command(label="تقارير الصيانة", command=self.show_maintenance_reports)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول النظام", command=self.show_about)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # شريط الحالة العلوي
        status_frame = ttk_bs.Frame(self.window, bootstyle=PRIMARY)
        status_frame.pack(fill=X, padx=5, pady=5)
        
        user_info = f"المستخدم: {self.auth_manager.current_user['full_name']} | الصلاحية: {self.auth_manager.current_user['role']}"
        ttk_bs.Label(status_frame, text=user_info, bootstyle=PRIMARY).pack(side=LEFT)
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء دفتر الملاحظات (Notebook) للتبويبات
        self.notebook = ttk_bs.Notebook(main_frame)
        self.notebook.pack(fill=BOTH, expand=True)
        
        # تبويب لوحة التحكم
        self.dashboard_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text="لوحة التحكم")
        
        # تبويب المشاريع
        self.projects_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.projects_frame, text="المشاريع")
        
        # تبويب المباني
        self.buildings_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.buildings_frame, text="المباني والمرافق")
        
        # تبويب الصيانة
        self.maintenance_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.maintenance_frame, text="الصيانة")
        
        # إنشاء محتوى لوحة التحكم
        self.create_dashboard()
    
    def create_dashboard(self):
        """إنشاء لوحة التحكم"""
        # عنوان لوحة التحكم
        title_label = ttk_bs.Label(
            self.dashboard_frame,
            text="لوحة التحكم الرئيسية",
            font=("Arial", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=10)
        
        # إطار الإحصائيات
        stats_frame = ttk_bs.LabelFrame(self.dashboard_frame, text="الإحصائيات السريعة", padding=10)
        stats_frame.pack(fill=X, padx=10, pady=5)
        
        # إحصائيات في صفوف
        stats_row1 = ttk_bs.Frame(stats_frame)
        stats_row1.pack(fill=X, pady=5)
        
        # بطاقات الإحصائيات
        self.create_stat_card(stats_row1, "المشاريع النشطة", "0", SUCCESS)
        self.create_stat_card(stats_row1, "البلاغات المفتوحة", "0", WARNING)
        self.create_stat_card(stats_row1, "المباني المسجلة", "0", INFO)
        self.create_stat_card(stats_row1, "أعمال الصيانة", "0", SECONDARY)
        
        # إطار الإشعارات
        notifications_frame = ttk_bs.LabelFrame(self.dashboard_frame, text="الإشعارات والتنبيهات", padding=10)
        notifications_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # قائمة الإشعارات
        self.notifications_tree = ttk_bs.Treeview(
            notifications_frame,
            columns=("type", "message", "date"),
            show="headings",
            height=8
        )
        
        self.notifications_tree.heading("type", text="النوع")
        self.notifications_tree.heading("message", text="الرسالة")
        self.notifications_tree.heading("date", text="التاريخ")
        
        self.notifications_tree.column("type", width=100)
        self.notifications_tree.column("message", width=400)
        self.notifications_tree.column("date", width=150)
        
        self.notifications_tree.pack(fill=BOTH, expand=True)
        
        # تحديث الإحصائيات
        self.update_dashboard_stats()
    
    def create_stat_card(self, parent, title, value, style):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk_bs.Frame(parent, bootstyle=style)
        card_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=5)
        
        ttk_bs.Label(card_frame, text=title, font=("Arial", 10)).pack(pady=2)
        ttk_bs.Label(card_frame, text=value, font=("Arial", 14, "bold"), bootstyle=style).pack(pady=2)
    
    def update_dashboard_stats(self):
        """تحديث إحصائيات لوحة التحكم"""
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        pass
    
    # وظائف القوائم (ستتم إضافتها لاحقاً)
    def show_settings(self): pass
    def logout(self): 
        self.auth_manager.logout()
        self.window.destroy()
        self.start()
    
    def show_projects(self):
        """عرض نافذة إدارة المشاريع"""
        from simple_project_management import SimpleProjectManagementWindow
        projects_window = SimpleProjectManagementWindow(self.window, self.db_manager, self.auth_manager)
        projects_window.show()

    def new_project(self):
        """إضافة مشروع جديد"""
        from simple_project_management import SimpleProjectDialog
        dialog = SimpleProjectDialog(self.window, self.db_manager, self.auth_manager, "إضافة مشروع جديد")
        dialog.show()
    def show_buildings(self): pass
    def new_building(self): pass
    def show_maintenance_requests(self): pass
    def show_preventive_maintenance(self): pass
    def show_maintenance_history(self): pass
    def show_dashboard(self): pass
    def show_project_reports(self): pass
    def show_maintenance_reports(self): pass
    def show_help(self): pass
    def show_about(self):
        messagebox.showinfo("حول النظام", 
                          "نظام إدارة أعمال الإدارة الهندسية\nالإصدار 1.0\nتطوير: فريق التطوير")

if __name__ == "__main__":
    app = MainApplication()
    app.start()
