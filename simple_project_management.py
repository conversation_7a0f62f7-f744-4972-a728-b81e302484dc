"""
وحدة إدارة المشاريع الهندسية - نسخة مبسطة
Simple Project Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from datetime import datetime

class SimpleProjectManagementWindow:
    """نافذة إدارة المشاريع المبسطة"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.projects_tree = None
        
    def show(self):
        """عرض نافذة إدارة المشاريع"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة المشاريع الهندسية")
        self.window.geometry("1200x700")
        self.window.resizable(True, True)
        
        self.create_interface()
        self.load_projects()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة المشاريع"""
        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(self.window)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk_bs.Button(
            toolbar_frame,
            text="مشروع جديد",
            command=self.add_project
        ).pack(side=tk.LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تعديل",
            command=self.edit_project
        ).pack(side=tk.LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="حذف",
            command=self.delete_project
        ).pack(side=tk.LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تفاصيل",
            command=self.view_project_details
        ).pack(side=tk.LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تحديث",
            command=self.load_projects
        ).pack(side=tk.LEFT, padx=2)
        
        # شريط البحث
        search_frame = ttk_bs.Frame(self.window)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk_bs.Label(search_frame, text="البحث:").pack(side=tk.LEFT, padx=2)
        self.search_entry = ttk_bs.Entry(search_frame, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=2)
        self.search_entry.bind('<KeyRelease>', self.filter_projects)
        
        # جدول المشاريع
        tree_frame = ttk_bs.Frame(self.window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # إنشاء Treeview
        columns = ("id", "name", "location", "type", "cost", "contractor", "start_date", "end_date", "status", "progress")
        self.projects_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=20)
        
        # تعريف العناوين
        headers = {
            "id": "المعرف",
            "name": "اسم المشروع",
            "location": "الموقع",
            "type": "النوع",
            "cost": "الكلفة",
            "contractor": "المقاول",
            "start_date": "تاريخ البداية",
            "end_date": "تاريخ الانتهاء",
            "status": "الحالة",
            "progress": "نسبة الإنجاز"
        }
        
        for col, header in headers.items():
            self.projects_tree.heading(col, text=header)
            if col == "id":
                self.projects_tree.column(col, width=50)
            elif col in ["name", "location", "contractor"]:
                self.projects_tree.column(col, width=150)
            else:
                self.projects_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_y = tk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.projects_tree.yview)
        scrollbar_x = tk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.projects_tree.xview)
        self.projects_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تعبئة الجدول وشريط التمرير
        self.projects_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط النقر المزدوج
        self.projects_tree.bind('<Double-1>', lambda e: self.view_project_details())
    
    def load_projects(self):
        """تحميل المشاريع من قاعدة البيانات"""
        if not self.projects_tree:
            return
            
        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)
        
        # تحميل البيانات الجديدة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT id, name, location, project_type, cost, contractor, 
                       start_date, end_date, status, progress_percentage
                FROM projects
                ORDER BY created_at DESC
            ''')
            
            projects = cursor.fetchall()
            
            for project in projects:
                # تنسيق البيانات
                project_id, name, location, project_type, cost, contractor, start_date, end_date, status, progress = project
                
                # تنسيق الكلفة
                cost_formatted = f"{cost:,.0f}" if cost else ""
                
                # تنسيق نسبة الإنجاز
                progress_formatted = f"{progress}%" if progress is not None else "0%"
                
                # إدراج في الجدول
                self.projects_tree.insert('', 'end', values=(
                    project_id, name or "", location or "", project_type or "",
                    cost_formatted, contractor or "", start_date or "", end_date or "",
                    status or "", progress_formatted
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشاريع: {str(e)}")
        finally:
            conn.close()
    
    def filter_projects(self, event=None):
        """فلترة المشاريع حسب النص المدخل"""
        search_text = self.search_entry.get().lower()
        
        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)
        
        # تحميل البيانات المفلترة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if search_text:
                cursor.execute('''
                    SELECT id, name, location, project_type, cost, contractor, 
                           start_date, end_date, status, progress_percentage
                    FROM projects
                    WHERE LOWER(name) LIKE ? OR LOWER(location) LIKE ? OR LOWER(contractor) LIKE ?
                    ORDER BY created_at DESC
                ''', (f'%{search_text}%', f'%{search_text}%', f'%{search_text}%'))
            else:
                cursor.execute('''
                    SELECT id, name, location, project_type, cost, contractor, 
                           start_date, end_date, status, progress_percentage
                    FROM projects
                    ORDER BY created_at DESC
                ''')
            
            projects = cursor.fetchall()
            
            for project in projects:
                project_id, name, location, project_type, cost, contractor, start_date, end_date, status, progress = project
                
                cost_formatted = f"{cost:,.0f}" if cost else ""
                progress_formatted = f"{progress}%" if progress is not None else "0%"
                
                self.projects_tree.insert('', 'end', values=(
                    project_id, name or "", location or "", project_type or "",
                    cost_formatted, contractor or "", start_date or "", end_date or "",
                    status or "", progress_formatted
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
        finally:
            conn.close()
    
    def add_project(self):
        """إضافة مشروع جديد"""
        dialog = SimpleProjectDialog(self.window, self.db_manager, self.auth_manager, "إضافة مشروع جديد")
        if dialog.show():
            self.load_projects()
    
    def edit_project(self):
        """تعديل مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        
        # جلب بيانات المشروع
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT * FROM projects WHERE id = ?', (project_id,))
            project_data = cursor.fetchone()
            
            if project_data:
                # تحويل إلى قاموس
                columns = [description[0] for description in cursor.description]
                project_dict = dict(zip(columns, project_data))
                
                dialog = SimpleProjectDialog(self.window, self.db_manager, self.auth_manager, "تعديل المشروع", project_dict)
                if dialog.show():
                    self.load_projects()
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على المشروع")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في جلب بيانات المشروع: {str(e)}")
        finally:
            conn.close()
    
    def delete_project(self):
        """حذف مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المشروع '{project_name}'؟"):
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute('DELETE FROM projects WHERE id = ?', (project_id,))
                conn.commit()
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المشروع: {str(e)}")
            finally:
                conn.close()
    
    def view_project_details(self):
        """عرض تفاصيل المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لعرض التفاصيل")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        messagebox.showinfo("تفاصيل المشروع", f"سيتم تطوير نافذة تفاصيل المشروع رقم {project_id} قريباً")


class SimpleProjectDialog:
    """نافذة إضافة/تعديل مشروع مبسطة"""
    
    def __init__(self, parent, db_manager, auth_manager, title, project_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.project_data = project_data
        self.result = False
        self.window = None
        
    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("500x600")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()
        
        self.create_form()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
        
        self.window.wait_window()
        return self.result
    
    def create_form(self):
        """إنشاء نموذج المشروع"""
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # اسم المشروع
        ttk_bs.Label(main_frame, text="اسم المشروع: *").pack(anchor=tk.W, pady=(0, 5))
        self.name_entry = ttk_bs.Entry(main_frame, width=50)
        self.name_entry.pack(pady=(0, 10))
        
        # وصف المشروع
        ttk_bs.Label(main_frame, text="وصف المشروع:").pack(anchor=tk.W, pady=(0, 5))
        self.description_text = tk.Text(main_frame, height=4, width=50)
        self.description_text.pack(pady=(0, 10))
        
        # موقع المشروع
        ttk_bs.Label(main_frame, text="موقع المشروع:").pack(anchor=tk.W, pady=(0, 5))
        self.location_entry = ttk_bs.Entry(main_frame, width=50)
        self.location_entry.pack(pady=(0, 10))
        
        # نوع المشروع
        ttk_bs.Label(main_frame, text="نوع المشروع:").pack(anchor=tk.W, pady=(0, 5))
        self.type_entry = ttk_bs.Entry(main_frame, width=50)
        self.type_entry.pack(pady=(0, 10))
        
        # الكلفة
        ttk_bs.Label(main_frame, text="الكلفة المقدرة:").pack(anchor=tk.W, pady=(0, 5))
        self.cost_entry = ttk_bs.Entry(main_frame, width=50)
        self.cost_entry.pack(pady=(0, 10))
        
        # المقاول
        ttk_bs.Label(main_frame, text="المقاول:").pack(anchor=tk.W, pady=(0, 5))
        self.contractor_entry = ttk_bs.Entry(main_frame, width=50)
        self.contractor_entry.pack(pady=(0, 10))
        
        # ملء البيانات إذا كان تعديل
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.description_text.insert('1.0', self.project_data.get('description', '') or '')
            self.location_entry.insert(0, self.project_data.get('location', '') or '')
            self.type_entry.insert(0, self.project_data.get('project_type', '') or '')
            self.cost_entry.insert(0, str(self.project_data.get('cost', '') or ''))
            self.contractor_entry.insert(0, self.project_data.get('contractor', '') or '')
        
        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        ttk_bs.Button(
            button_frame,
            text="حفظ",
            command=self.save_project
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk_bs.Button(
            button_frame,
            text="إلغاء",
            command=self.window.destroy
        ).pack(side=tk.RIGHT)
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        name = self.name_entry.get().strip()
        description = self.description_text.get('1.0', tk.END).strip()
        location = self.location_entry.get().strip()
        project_type = self.type_entry.get().strip()
        cost_str = self.cost_entry.get().strip()
        contractor = self.contractor_entry.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        # تحويل الكلفة إلى رقم
        cost = None
        if cost_str:
            try:
                cost = float(cost_str)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال كلفة صحيحة")
                return
        
        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.project_data:  # تعديل
                cursor.execute('''
                    UPDATE projects 
                    SET name=?, description=?, location=?, project_type=?, cost=?,
                        contractor=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (name, description or None, location or None, project_type or None,
                      cost, contractor or None, self.project_data['id']))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO projects (name, description, location, project_type, cost,
                                        contractor, status, progress_percentage, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (name, description or None, location or None, project_type or None,
                      cost, contractor or None, 'planning', 0, self.auth_manager.current_user['id']))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المشروع بنجاح")
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()
